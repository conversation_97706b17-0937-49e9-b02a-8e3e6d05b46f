"use client";

import * as React from "react";
import { useDroppable } from "@dnd-kit/core";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { History } from "lucide-react";
import { DraggableChatItem } from "./draggable-chat-item";
import { cn } from "@/lib/utils";

interface DroppableChatHistoryProps {
  chatHistory: any[];
  editingIndex: number | null;
  editingName: string;
  setEditingName: (name: string) => void;
  handleNameSubmit: (chat: any) => void;
  setEditingIndex: (index: number | null) => void;
  openDropdown: number | null;
  setOpenDropdown: (index: number | null) => void;
  hoveredProject: number | null;
  setHoveredProject: (index: number | null) => void;
  groups: any[];
  handleStartEditing: (index: number, name: string) => void;
  router: any;
  isChatActive: (chatId: string) => boolean;
  isOver?: boolean;
}

export function DroppableChatHistory({
  chatHistory,
  editingIndex,
  editingName,
  setEditingName,
  handleNameSubmit,
  setEditingIndex,
  openDropdown,
  setOpenDropdown,
  hoveredProject,
  setHoveredProject,
  groups,
  handleStartEditing,
  router,
  isChatActive,
  isOver = false,
}: DroppableChatHistoryProps) {
  const { setNodeRef } = useDroppable({
    id: "chat-history",
    data: {
      type: "chat-history",
    },
  });

  const chatIds = chatHistory.map((chat) => `chat-${chat.id}`);

  return (
    <div className="h-full">
      <div className="relative flex w-full min-w-0 flex-col">
        <div
          ref={setNodeRef}
          className={cn(
            "flex w-full min-w-0 flex-col gap-1 min-h-[100px] p-2 rounded-md transition-colors",
            isOver && "bg-primary/5 ring-1 ring-primary/20"
          )}
        >
          <SortableContext items={chatIds} strategy={verticalListSortingStrategy}>
            {chatHistory?.map((item: any, index: number) => {
              const chatIsActive = isChatActive(item?.id);
              
              return (
                <DraggableChatItem
                  key={item.id}
                  chat={{
                    id: item?.id,
                    title: item?.name,
                    url: item?.url,
                  }}
                  chatIndex={index}
                  isActive={chatIsActive}
                  editingIndex={editingIndex}
                  editingName={editingName}
                  setEditingName={setEditingName}
                  handleNameSubmit={handleNameSubmit}
                  setEditingIndex={setEditingIndex}
                  openDropdown={openDropdown}
                  setOpenDropdown={setOpenDropdown}
                  hoveredProject={hoveredProject}
                  setHoveredProject={setHoveredProject}
                  groups={groups}
                  handleStartEditing={handleStartEditing}
                  router={router}
                />
              );
            })}
          </SortableContext>
          
          {/* Drop indicator when over */}
          {isOver && (
            <div className="absolute inset-0 border-2 border-dashed border-primary/50 rounded-md bg-primary/5 flex items-center justify-center">
              <div className="text-center">
                <History className="h-6 w-6 mx-auto mb-2 text-primary" />
                <p className="text-sm text-primary font-medium">
                  Drop to remove from group
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
