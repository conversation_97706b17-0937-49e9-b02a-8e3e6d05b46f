"use client";

import * as React from "react";
import {
  Folder,
  Plus,
  FileText,
  History,
  Users,
} from "lucide-react";
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners,
  UniqueIdentifier,
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
// Import removed since we're implementing custom scrolling
import { NavProjects } from "@/components/layouts/nav-projects";
import { NavSecondary } from "@/components/layouts/nav-secondary";
import { NavUser } from "@/components/layouts/nav-user";
import {
  Sidebar,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
} from "@/components/layouts/sidebar";
import { OrganizationSwitcher } from "@/components/organizations/organization-switcher";
import { But<PERSON> } from "../ui/button";
import { CreateGroupDialog } from "../model/create-group-dialog";
import { usePathname, useRouter } from "next/navigation";
import { updateChat } from "@/services";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";
import { DroppableGroup } from "./droppable-group";
import { DroppableChatHistory } from "./droppable-chat-history";

// Default workspaces - adding more to test scrolling

const data = {
  user: {
    name: "SKH ",
    role: "Admin",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [],
  navSecondary: [
    {
      title: "Help",
      url: `${process.env.NEXT_PUBLIC_API_BASE_URL}/docs/en`,
      icon: FileText,
      translationKey: "sidebar.help",
    },
  ],
  projects: [
    {
      name: "Shared Threads",
      url: "/shared-threads",
      icon: Users,
      translationKey: "sidebar.sharedThreads",
    },
  ],
};

export function MemberAppSidebar({
  session,
  group,
  chatHistory,
  tenantId,
  ...props
}: any) {
  const [openDropdown, setOpenDropdown] = React.useState<number | null>(null);
  const [editingIndex, setEditingIndex] = React.useState<number | null>(null);
  const [editingName, setEditingName] = React.useState("");

  // Drag and drop state
  const [activeId, setActiveId] = React.useState<UniqueIdentifier | null>(null);
  const [overId, setOverId] = React.useState<UniqueIdentifier | null>(null);

  // Sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Drag and drop handlers
  const handleDragStart = (event: DragStartEvent) => {
    console.log("Drag started:", event.active.id, event.active.data.current);
    setActiveId(event.active.id);
  };

  const handleDragOver = (event: DragOverEvent) => {
    console.log("Drag over:", event.over?.id, event.over?.data.current);
    setOverId(event.over?.id || null);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    console.log("Drag ended:", {
      activeId: active.id,
      overId: over?.id,
      activeData: active.data.current,
      overData: over?.data.current,
    });

    setActiveId(null);
    setOverId(null);

    if (!over || active.id === over.id) {
      console.log("No valid drop target or same element");
      return;
    }

    const activeData = active.data.current;
    const overData = over.data.current;

    if (!activeData || activeData.type !== "chat") {
      console.log("Invalid active data or not a chat");
      return;
    }

    const chatId = activeData.chat.id;
    const currentGroupId = activeData.groupId;

    try {
      let newGroupId = null;

      // Determine the target group
      if (overData?.type === "group") {
        newGroupId = overData.groupId;
        console.log("Dropping into group:", newGroupId);
      } else if (over.id === "chat-history") {
        newGroupId = null; // Remove from group
        console.log("Dropping into chat history (ungrouping)");
      } else if (overData?.type === "chat") {
        newGroupId = overData.groupId || null;
        console.log("Dropping onto another chat, target group:", newGroupId);
      } else {
        console.log("Unknown drop target:", over.id, overData);
      }

      console.log("Group change:", currentGroupId, "->", newGroupId);

      // Only update if the group actually changed
      if (newGroupId !== currentGroupId) {
        toast.loading(t("chat.moving") || "Moving chat...");

        const response = await updateChat({
          id: chatId,
          groupId: newGroupId,
        });

        toast.remove();

        if (response.error) {
          toast.error(response.error);
        } else {
          toast.success(t("chat.moveSuccess") || "Chat moved successfully!");
          // Reload to reflect changes
          window.location.reload();
        }
      }
    } catch (error) {
      console.error("Drag and drop error:", error);
      toast.error(t("chat.moveFailed") || "Failed to move chat");
    }
  };

  const handleStartEditing = (index: number, name: string) => {
    setEditingIndex(index);
    setEditingName(name);
    setOpenDropdown(null);
  };

  const handleNameSubmit = async (chat: any) => {
    if (editingName.trim() === "") return;

    try {
      const response = await updateChat({
        id: chat.id,
        title: editingName,
      });
      if (response.error) {
        toast.error(response.error);
      } else {
        toast.success(t("chat.renameSuccess"));
        window.location.reload();
      }
    } catch (error) {
      toast.error(t("chat.renameFailed"));
    } finally {
      setEditingIndex(null);
    }
  };

  const pathname = usePathname() || "";
  const router = useRouter();
  const { t } = useLanguage();
  const [hoveredProject, setHoveredProject] = React.useState<number | null>(
    null
  );
  const user = { ...session?.user, role: session?.memberships?.[0]?.role };
  const [groups] = React.useState(() => {
    // Fall back to group prop if no temporary data
    return (
      group?.map((g: any) => ({
        id: g.id,
        title: g.name,
        icon: Folder,
        isActive: false,
        items: g?.chats?.map((chat: any) => ({
          id: chat.id,
          title: chat.title,
          url: `/ask-ai/${chat.id}/`,
        })),
      })) || []
    );
  });

  // State to track expanded groups
  const [expandedGroups, setExpandedGroups] = React.useState<
    Record<string, boolean>
  >({});

  // State to track group chats (using only the setter)
  const [, setGroupChats] = React.useState<Record<string, any[]>>({});

  // Toggle group expansion
  const toggleGroupExpansion = (groupName: string) => {
    setExpandedGroups((prev) => ({
      ...prev,
      [groupName]: !prev[groupName],
    }));
  };

  React.useEffect(() => {
    const group =
      groups?.find(
        (g: any) => g?.items?.find((c: any) => c?.url === pathname)
      ) || [];
    setExpandedGroups((prev) => ({
      ...prev,
      [group?.title]: true,
    }));
  }, [groups]);

  // Check if a group or its chat is active
  const isGroupActive = (groupId: string) => {
    return pathname.includes(`/ask-ai/${groupId}`);
  };

  // Check if a specific chat is active
  const isChatActive = (chatId: string) => {
    return pathname === `/ask-ai/${chatId}/`;
  };

  // Initialize chats from the group prop
  React.useEffect(() => {
    if (groups) {
      const newGroupChats: Record<string, any[]> = {};

      groups.forEach((g: any) => {
        if (g.id && g.chats) {
          newGroupChats[g.id] = g.chats.map((chat: any) => ({
            id: chat.id,
            name: chat.name,
            icon: Folder,
            type: "chat",
            createdAt: chat.createdAt,
          }));

          // Auto-expand group if it's active
          if (isGroupActive(g.id)) {
            setExpandedGroups((prev) => ({
              ...prev,
              [g.id]: true,
            }));
          }
        }
      });

      setGroupChats(newGroupChats);
    }
  }, [groups, pathname]); // Added pathname as dependency

  // Collect all chat IDs for the global SortableContext
  const allChatIds = React.useMemo(() => {
    const ids: string[] = [];

    // Add chat IDs from groups
    groups.forEach((g: any) => {
      if (g.items) {
        g.items.forEach((chat: any) => {
          ids.push(`chat-${chat.id}`);
        });
      }
    });

    // Add chat IDs from chat history
    if (chatHistory) {
      chatHistory.forEach((chat: any) => {
        ids.push(`chat-${chat.id}`);
      });
    }

    return ids;
  }, [groups, chatHistory]);

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCorners}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      <SortableContext items={allChatIds} strategy={verticalListSortingStrategy}>
        <Sidebar
          className="top-[--header-height] !h-[calc(100svh-var(--header-height))]"
          {...props}
        >
        <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <div className="px-2">
              <OrganizationSwitcher isAdmin={false} />
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <div className="flex min-h-0 flex-1 flex-col gap-2 p-2 overflow-hidden">
        <a href="/ask-ai" className="shrink-0 mb-2">
          <Button
            variant="secondary"
            size="sm"
            className="w-full justify-between rounded-full px-4"
          >
            {t("memberSidebar.newChat")}
            <Plus className="h-4 w-4 " />
          </Button>
        </a>{" "}
        <div className="shrink-0">
          <NavProjects projects={data.projects} />
        </div>
        <div className="flex items-center justify-between px-2 py-1 shrink-0">
          <div className="text-xs font-medium text-sidebar-foreground/70">
            {t("memberSidebar.chatHistory")}
          </div>

          <CreateGroupDialog
            trigger={
              <Button size="icon" variant="ghost">
                <Plus className="h-4 w-4" />
                <span className="sr-only">
                  {t("memberSidebar.createGroup")}
                </span>
              </Button>
            }
            tenantId={tenantId}
          />
        </div>
        <div className="overflow-y-auto flex w-full h-full min-w-0 flex-col">
          <ul className="flex w-full min-w-0 flex-col gap-1">
            {group.map((item: any) => {
              const groupName = item.name;
              const isActive = isGroupActive(item.id);
              const isExpanded = expandedGroups[groupName];
              const chats =
                groups.find((g: { id: string }) => g.id === item.id)?.items ||
                [];

              return (
                <DroppableGroup
                  key={item.id}
                  group={item}
                  chats={chats}
                  isActive={isActive}
                  isExpanded={isExpanded}
                  toggleGroupExpansion={toggleGroupExpansion}
                  editingIndex={editingIndex}
                  editingName={editingName}
                  setEditingName={setEditingName}
                  handleNameSubmit={handleNameSubmit}
                  setEditingIndex={setEditingIndex}
                  openDropdown={openDropdown}
                  setOpenDropdown={setOpenDropdown}
                  hoveredProject={hoveredProject}
                  setHoveredProject={setHoveredProject}
                  groups={groups}
                  handleStartEditing={handleStartEditing}
                  router={router}
                  isChatActive={isChatActive}
                  isOver={overId === `group-${item.id}`}
                />
              );
            })}
          </ul>

          <DroppableChatHistory
            chatHistory={chatHistory || []}
            editingIndex={editingIndex}
            editingName={editingName}
            setEditingName={setEditingName}
            handleNameSubmit={handleNameSubmit}
            setEditingIndex={setEditingIndex}
            openDropdown={openDropdown}
            setOpenDropdown={setOpenDropdown}
            hoveredProject={hoveredProject}
            setHoveredProject={setHoveredProject}
            groups={groups}
            handleStartEditing={handleStartEditing}
            router={router}
            isChatActive={isChatActive}
            isOver={overId === "chat-history"}
          />
        </div>
        <div className="mx-2 h-px bg-sidebar-border shrink-0" />
        <div className="shrink-0 mt-auto">
          <NavSecondary items={data.navSecondary} />
        </div>
      </div>
      <SidebarFooter>
        <NavUser
          user={{
            name: user?.name ?? user?.email,
            role: user?.role,
            avatar: user?.image,
          }}
        />
      </SidebarFooter>
    </Sidebar>
    </SortableContext>

    <DragOverlay>
      {activeId ? (
        <div className="bg-sidebar-accent text-sidebar-accent-foreground rounded-md p-2 shadow-lg border opacity-90">
          <div className="flex items-center gap-2">
            <History className="size-3.5 shrink-0" />
            <span className="text-sm">
              {(() => {
                // Find the chat being dragged
                const chatId = activeId.toString().replace('chat-', '');

                // Look in groups first
                for (const g of groups) {
                  const chat = g.items?.find((c: any) => c.id === chatId);
                  if (chat) return chat.title;
                }

                // Look in chat history
                const historyChat = chatHistory?.find((c: any) => c.id === chatId);
                if (historyChat) return historyChat.name;

                return 'Chat';
              })()}
            </span>
          </div>
        </div>
      ) : null}
    </DragOverlay>
    </DndContext>
  );
}
